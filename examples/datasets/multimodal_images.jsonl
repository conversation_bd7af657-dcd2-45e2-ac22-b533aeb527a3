{
  "_config": {
    "image_base_path": "./test_images",
    "default_image_quality": 85
  }
}
{
  "messages": [
    {
      "role": "user",
      "content": [
        {"type": "text", "text": "Describe this image in detail"},
        {"type": "image", "path": "nature/landscape1.jpg"}
      ]
    }
  ],
  "expected_tokens": 80,
  "metadata": {
    "id": "img_001",
    "category": "image_description"
  }
}
{
  "messages": [
    {
      "role": "user",
      "content": [
        {"type": "text", "text": "What objects can you see in this image?"},
        {"type": "image", "path": "objects/room1.jpg"}
      ]
    }
  ],
  "expected_tokens": 60,
  "metadata": {
    "id": "img_002",
    "category": "object_detection"
  }
}
{
  "messages": [
    {
      "role": "user",
      "content": [
        {"type": "text", "text": "Compare these two images and tell me the differences"},
        {"type": "image", "path": "comparison/before.jpg"},
        {"type": "image", "path": "comparison/after.jpg"}
      ]
    }
  ],
  "expected_tokens": 120,
  "metadata": {
    "id": "img_003",
    "category": "image_comparison"
  }
}
{
  "messages": [
    {
      "role": "user",
      "content": [
        {"type": "text", "text": "Read the text in this image"},
        {"type": "image", "path": "text/document1.jpg"}
      ]
    }
  ],
  "expected_tokens": 100,
  "metadata": {
    "id": "img_004",
    "category": "ocr"
  }
}
{
  "messages": [
    {
      "role": "user",
      "content": [
        {"type": "text", "text": "What is the mood or emotion conveyed in this image?"},
        {"type": "image", "path": "emotions/happy_scene.jpg"}
      ]
    }
  ],
  "expected_tokens": 70,
  "metadata": {
    "id": "img_005",
    "category": "emotion_analysis"
  }
}
