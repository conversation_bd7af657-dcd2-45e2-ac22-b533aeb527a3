{
  "_config": {
    "video_base_path": "./test_videos",
    "default_video_fps": 1.0
  }
}
{
  "messages": [
    {
      "role": "user",
      "content": [
        {"type": "text", "text": "Describe what happens in this video"},
        {
          "type": "video",
          "path": "actions/cooking.mp4",
          "options": {
            "fps": 2.0,
            "max_frames": 8
          }
        }
      ]
    }
  ],
  "expected_tokens": 150,
  "metadata": {
    "id": "vid_001",
    "category": "video_description"
  }
}
{
  "messages": [
    {
      "role": "user",
      "content": [
        {"type": "text", "text": "Count the number of people in this video"},
        {
          "type": "video",
          "path": "people/meeting.mp4",
          "options": {
            "fps": 0.5,
            "max_frames": 5
          }
        }
      ]
    }
  ],
  "expected_tokens": 50,
  "metadata": {
    "id": "vid_002",
    "category": "counting"
  }
}
{
  "messages": [
    {
      "role": "user",
      "content": [
        {"type": "text", "text": "Analyze the movement patterns in this video"},
        {
          "type": "video",
          "path": "sports/basketball.mp4",
          "options": {
            "fps": 3.0,
            "max_frames": 12,
            "start_time": 10,
            "duration": 20
          }
        }
      ]
    }
  ],
  "expected_tokens": 200,
  "metadata": {
    "id": "vid_003",
    "category": "motion_analysis"
  }
}
{
  "messages": [
    {
      "role": "user",
      "content": [
        {"type": "text", "text": "What safety issues can you identify in this video?"},
        {
          "type": "video",
          "path": "safety/construction.mp4",
          "options": {
            "fps": 1.0,
            "max_frames": 6
          }
        }
      ]
    }
  ],
  "expected_tokens": 120,
  "metadata": {
    "id": "vid_004",
    "category": "safety_analysis"
  }
}
