{
  "_config": {
    "image_base_path": "./test_images",
    "video_base_path": "./test_videos",
    "default_image_quality": 85,
    "default_video_fps": 1.0
  }
}
{
  "messages": [
    {"role": "system", "content": "You are a helpful assistant that can analyze both images and videos."},
    {
      "role": "user",
      "content": [
        {"type": "text", "text": "Compare this image and video, what similarities do you see?"},
        {"type": "image", "path": "scenes/park_photo.jpg"},
        {"type": "video", "path": "scenes/park_video.mp4"}
      ]
    }
  ],
  "expected_tokens": 180,
  "metadata": {
    "id": "mixed_001",
    "category": "cross_modal_comparison"
  }
}
{
  "messages": [
    {
      "role": "user",
      "content": [
        {"type": "text", "text": "Based on this product image and demo video, write a product review"},
        {"type": "image", "path": "products/phone_image.jpg"},
        {"type": "video", "path": "products/phone_demo.mp4"}
      ]
    }
  ],
  "expected_tokens": 250,
  "metadata": {
    "id": "mixed_002",
    "category": "product_review"
  }
}
{
  "messages": [
    {
      "role": "user",
      "content": [
        {"type": "text", "text": "Tell me a story that connects these images and video"},
        {"type": "image", "path": "story/character1.jpg"},
        {"type": "image", "path": "story/character2.jpg"},
        {"type": "video", "path": "story/scene.mp4"}
      ]
    }
  ],
  "expected_tokens": 300,
  "metadata": {
    "id": "mixed_003",
    "category": "storytelling"
  }
}
{
  "messages": [
    {
      "role": "user",
      "content": [
        {"type": "text", "text": "Analyze this medical case with the provided image and video"},
        {"type": "image", "path": "medical/xray.jpg"},
        {"type": "video", "path": "medical/procedure.mp4"}
      ]
    }
  ],
  "expected_tokens": 200,
  "metadata": {
    "id": "mixed_004",
    "category": "medical_analysis"
  }
}
