# 多模态数据集格式规范

## 概述

stress_tool 支持多种数据集格式，既兼容现有的纯文本格式，也支持包含图片、视频等多模态输入的复杂格式。

## 1. 纯文本数据集（兼容现有格式）

### 1.1 简单格式
```jsonl
{"prompt": "What is the capital of France?", "expected_tokens": 10}
{"prompt": "Explain quantum computing", "expected_tokens": 200}
```

### 1.2 OpenAI Chat 格式
```jsonl
{
  "messages": [
    {"role": "system", "content": "You are a helpful assistant."},
    {"role": "user", "content": "What is the capital of France?"}
  ],
  "expected_tokens": 10
}
```

## 2. 多模态数据集格式

### 2.1 基本结构
```jsonl
{
  "messages": [
    {
      "role": "user",
      "content": [
        {"type": "text", "text": "描述这张图片"},
        {"type": "image", "path": "images/sample1.jpg"}
      ]
    }
  ],
  "expected_tokens": 50,
  "metadata": {
    "id": "sample_001",
    "category": "image_description"
  }
}
```

### 2.2 支持的内容类型

#### 2.2.1 文本内容
```json
{"type": "text", "text": "这是文本内容"}
```

#### 2.2.2 图片内容
```json
{
  "type": "image",
  "path": "images/sample1.jpg",
  "description": "可选的图片描述",
  "resize": {
    "max_width": 1024,
    "max_height": 1024,
    "quality": 85
  }
}
```

支持的图片格式：
- JPEG (.jpg, .jpeg)
- PNG (.png)
- WebP (.webp)
- BMP (.bmp)
- TIFF (.tiff, .tif)

#### 2.2.3 视频内容
```json
{
  "type": "video",
  "path": "videos/sample1.mp4",
  "description": "可选的视频描述",
  "options": {
    "fps": 1.0,
    "max_frames": 10,
    "start_time": 0,
    "duration": 30
  }
}
```

支持的视频格式：
- MP4 (.mp4)
- AVI (.avi)
- MOV (.mov)
- MKV (.mkv)
- WebM (.webm)

#### 2.2.4 Base64 编码内容
```json
{
  "type": "image_url",
  "image_url": {
    "url": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD..."
  }
}
```

### 2.3 复杂示例

#### 2.3.1 多图片对比
```jsonl
{
  "messages": [
    {
      "role": "user",
      "content": [
        {"type": "text", "text": "比较这两张图片的差异"},
        {"type": "image", "path": "images/before.jpg"},
        {"type": "image", "path": "images/after.jpg"}
      ]
    }
  ],
  "expected_tokens": 100
}
```

#### 2.3.2 视频分析
```jsonl
{
  "messages": [
    {
      "role": "user",
      "content": [
        {"type": "text", "text": "分析这个视频中发生了什么"},
        {
          "type": "video",
          "path": "videos/action.mp4",
          "options": {
            "fps": 2.0,
            "max_frames": 8
          }
        }
      ]
    }
  ],
  "expected_tokens": 150
}
```

#### 2.3.3 混合模态对话
```jsonl
{
  "messages": [
    {"role": "system", "content": "你是一个专业的图像和视频分析助手。"},
    {
      "role": "user",
      "content": [
        {"type": "text", "text": "请分析这张图片和视频的关联性"},
        {"type": "image", "path": "images/scene.jpg"},
        {"type": "video", "path": "videos/scene_video.mp4"}
      ]
    }
  ],
  "expected_tokens": 200
}
```

## 3. 配置选项

### 3.1 全局配置
在数据集文件的第一行可以包含全局配置：
```jsonl
{
  "_config": {
    "image_base_path": "./test_images",
    "video_base_path": "./test_videos",
    "default_image_quality": 85,
    "default_video_fps": 1.0
  }
}
```

### 3.2 路径解析规则
1. 绝对路径：直接使用
2. 相对路径：相对于数据集文件所在目录
3. 如果设置了 `image_base_path` 或 `video_base_path`，则相对于这些基础路径

## 4. 错误处理

### 4.1 文件不存在
如果指定的图片或视频文件不存在，该条数据将被跳过，并在日志中记录错误。

### 4.2 格式不支持
如果文件格式不受支持，将尝试使用默认处理方式，失败则跳过。

### 4.3 处理失败
如果图片或视频处理失败（如损坏的文件），将跳过该条数据并记录错误。

## 5. 性能优化建议

### 5.1 图片优化
- 使用适当的图片尺寸，避免过大的图片
- 选择合适的压缩质量，平衡文件大小和质量
- 批量预处理图片，避免运行时重复处理

### 5.2 视频优化
- 合理设置 FPS，避免提取过多帧
- 限制视频时长，专注于关键片段
- 预先转换为支持的格式

### 5.3 缓存策略
- 相同文件的多次引用将使用缓存
- Base64 编码结果会被缓存
- 处理后的图片会被临时缓存
