# stress_tool 产品设计文档

## 1. 产品概述

### 1.1 产品定位
stress_tool 是一个专为大语言模型推理服务设计的性能压测工具，支持流式和非流式场景，提供详细的性能指标分析。

### 1.2 核心价值
- **专业性**：针对 LLM 推理服务的特殊需求（流式返回、token 计算等）
- **准确性**：本地 tokenizer 预估，精确的延迟测量
- **易用性**：简单的命令行接口，丰富的输出报告
- **可扩展性**：支持多种数据集格式和配置选项

## 2. 功能需求

### 2.1 核心功能
- **并发压测**：支持可配置的并发数
- **流式支持**：完整支持 SSE 流式响应
- **多指标统计**：延迟、吞吐量、成功率等
- **失败分析**：详细的错误统计和日志

### 2.2 输入参数
```rust
pub struct Config {
    pub model: String,           // 模型名称
    pub api_base: String,        // API 基础地址
    pub concurrency: u32,        // 并发数
    pub total_requests: u32,     // 总请求数
    pub dataset: String,         // 数据集文件路径
    pub max_tokens: u32,         // 最大生成 token 数
    pub temperature: f32,        // 采样温度
    pub stream: bool,            // 是否流式
    pub timeout: u64,            // 请求超时时间（秒）
}
```

### 2.3 输出指标
详见 `readme.md` 中的指标说明，包括：
- 请求统计（成功/失败数量）
- 时间指标（延迟分布、TTFT、每 token 时间）
- 吞吐量指标（QPS、tokens/sec）
- 系统资源快照

## 3. 技术架构

### 3.1 技术栈
- **语言**：Rust（高性能、内存安全）
- **异步运行时**：tokio（并发处理）
- **HTTP 客户端**：reqwest（支持 SSE）
- **JSON 处理**：serde_json
- **Tokenizer**：tiktoken-rs 或 tokenizers

### 3.2 核心模块
```
src/
├── main.rs              // 入口点和 CLI 解析
├── config.rs            // 配置管理
├── client.rs            // HTTP 客户端封装
├── tokenizer.rs         // Token 计算
├── metrics.rs           // 指标收集和统计
├── dataset.rs           // 数据集加载
├── runner.rs            // 压测执行器
└── reporter.rs          // 结果输出
```

### 3.3 数据流
1. 加载配置和数据集
2. 初始化 tokenizer 和 HTTP 客户端
3. 启动并发任务执行请求
4. 实时收集指标数据
5. 生成统计报告和日志

## 4. 用户体验设计

### 4.1 命令行接口
```bash
stress_tool [OPTIONS] --url <URL>

OPTIONS:
    -m, --model <MODEL>              模型名称 [default: gpt-3.5-turbo]
    -u, --url <URL>                  API 端点 URL
    -c, --concurrency <NUM>          并发数 [default: 1]
    -n, --number <NUM>               总请求数 [default: 100]
    -d, --dataset <FILE>             数据集文件路径
    -t, --max-tokens <NUM>           最大生成 tokens [default: 512]
        --temperature <FLOAT>        采样温度 [default: 0.1]
        --stream                     启用流式模式
        --timeout <SECONDS>          请求超时时间 [default: 30]
    -o, --output <DIR>               输出目录 [default: benchmark]
    -h, --help                       显示帮助信息
```

### 4.2 输出格式
- **实时进度**：显示当前进度和实时 QPS
- **结果文件**：JSON 格式的详细报告
- **日志文件**：包含所有请求的详细日志

## 5. 实现计划

### 5.1 MVP 阶段（v0.1.0）
- [ ] 基础 CLI 参数解析
- [ ] HTTP 客户端实现
- [ ] 简单的并发压测
- [ ] 基础指标统计

### 5.2 完善阶段（v0.2.0）
- [ ] 流式响应支持
- [ ] Tokenizer 集成
- [ ] 详细的延迟统计
- [ ] 错误处理和重试

### 5.3 优化阶段（v0.3.0）
- [ ] 性能优化
- [ ] 更多数据集格式支持
- [ ] 可视化报告
- [ ] 配置文件支持

## 6. 质量保证

### 6.1 测试策略
- 单元测试：各模块功能测试
- 集成测试：端到端压测流程
- 性能测试：工具本身的性能验证

### 6.2 错误处理
- 网络错误重试机制
- 优雅的超时处理
- 详细的错误日志记录

## 7. 部署和分发

### 7.1 构建方式
- 支持多平台编译（Linux、macOS、Windows）
- 提供预编译二进制文件
- 支持 Docker 容器化部署

### 7.2 配置管理
- 命令行参数优先级最高
- 支持配置文件（YAML/JSON）
- 环境变量支持