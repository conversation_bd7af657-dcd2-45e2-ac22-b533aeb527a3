# stress_tool 产品设计文档

## 1. 产品概述

### 1.1 产品定位
stress_tool 是一个专为大语言模型和多模态模型推理服务设计的性能压测工具，支持文本、图片、视频等多种输入模态，支持流式和非流式场景，提供详细的性能指标分析。

### 1.2 核心价值
- **专业性**：针对 LLM 和多模态模型推理服务的特殊需求（流式返回、token 计算、多媒体处理等）
- **准确性**：本地 tokenizer 预估，精确的延迟测量，多模态输入处理
- **易用性**：简单的命令行接口，丰富的输出报告
- **可扩展性**：支持多种数据集格式和配置选项，灵活的多模态输入组合

## 2. 功能需求

### 2.1 核心功能
- **并发压测**：支持可配置的并发数
- **流式支持**：完整支持 SSE 流式响应
- **多模态输入**：支持文本、图片、视频等多种输入模态的组合
- **多指标统计**：延迟、吞吐量、成功率等
- **失败分析**：详细的错误统计和日志

### 2.2 输入参数
```rust
pub struct Config {
    pub model: String,           // 模型名称
    pub api_base: String,        // API 基础地址
    pub concurrency: u32,        // 并发数
    pub total_requests: u32,     // 总请求数
    pub dataset: String,         // 数据集文件路径
    pub max_tokens: u32,         // 最大生成 token 数
    pub temperature: f32,        // 采样温度
    pub stream: bool,            // 是否流式
    pub timeout: u64,            // 请求超时时间（秒）

    // 多模态相关配置
    pub multimodal: bool,        // 是否启用多模态模式
    pub image_base_path: Option<String>, // 图片文件基础路径
    pub video_base_path: Option<String>, // 视频文件基础路径
    pub max_image_size: Option<u32>,     // 最大图片尺寸（像素）
    pub image_quality: Option<u8>,       // 图片质量（1-100）
}
```

### 2.3 输出指标
详见 `readme.md` 中的指标说明，包括：
- 请求统计（成功/失败数量）
- 时间指标（延迟分布、TTFT、每 token 时间）
- 吞吐量指标（QPS、tokens/sec）
- 多模态指标（图片处理时间、视频处理时间、多媒体数据传输量）
- 系统资源快照

## 3. 技术架构

### 3.1 技术栈
- **语言**：Rust（高性能、内存安全）
- **异步运行时**：tokio（并发处理）
- **HTTP 客户端**：reqwest（支持 SSE）
- **JSON 处理**：serde_json
- **Tokenizer**：tiktoken-rs 或 tokenizers
- **图像处理**：image crate（图片加载、格式转换、尺寸调整）
- **视频处理**：ffmpeg-next 或 opencv（视频帧提取、格式转换）
- **Base64 编码**：base64 crate（多媒体数据编码）

### 3.2 核心模块
```
src/
├── main.rs              // 入口点和 CLI 解析
├── config.rs            // 配置管理
├── client.rs            // HTTP 客户端封装
├── tokenizer.rs         // Token 计算
├── metrics.rs           // 指标收集和统计
├── dataset.rs           // 数据集加载
├── runner.rs            // 压测执行器
├── reporter.rs          // 结果输出
└── multimodal/          // 多模态处理模块
    ├── mod.rs           // 模块入口
    ├── image.rs         // 图片处理（加载、转换、编码）
    ├── video.rs         // 视频处理（帧提取、编码）
    ├── request.rs       // 多模态请求构建
    └── utils.rs         // 多模态工具函数
```

### 3.3 数据流
1. 加载配置和数据集
2. 初始化 tokenizer 和 HTTP 客户端
3. 预处理多模态数据（图片/视频加载、格式转换、Base64编码）
4. 启动并发任务执行请求
5. 实时收集指标数据（包括多模态处理时间）
6. 生成统计报告和日志

## 4. 多模态功能设计

### 4.1 支持的输入模态
- **文本**：纯文本输入，与现有功能兼容
- **图片**：支持 JPEG、PNG、WebP 等常见格式
- **视频**：支持 MP4、AVI、MOV 等格式（提取关键帧）
- **混合模态**：文本+图片、文本+视频、文本+图片+视频等组合

### 4.2 数据集格式
#### 4.2.1 纯文本数据集（兼容现有格式）
```jsonl
{"prompt": "What is the capital of France?", "expected_tokens": 10}
{"prompt": "Explain quantum computing", "expected_tokens": 200}
```

#### 4.2.2 多模态数据集格式
```jsonl
{
  "messages": [
    {"role": "user", "content": [
      {"type": "text", "text": "Describe this image"},
      {"type": "image", "path": "images/sample1.jpg"}
    ]}
  ],
  "expected_tokens": 50
}
{
  "messages": [
    {"role": "user", "content": [
      {"type": "text", "text": "What happens in this video?"},
      {"type": "video", "path": "videos/sample1.mp4", "fps": 1.0}
    ]}
  ],
  "expected_tokens": 100
}
```

### 4.3 API 请求格式
支持 OpenAI Vision API 兼容格式：
```json
{
  "model": "qwen2.5-vl-32b-instruct",
  "messages": [
    {
      "role": "user",
      "content": [
        {"type": "text", "text": "What's in this image?"},
        {"type": "image_url", "image_url": {"url": "data:image/jpeg;base64,..."}}
      ]
    }
  ],
  "max_tokens": 100
}
```

### 4.4 性能优化策略
- **图片预处理**：批量加载和缓存，避免重复处理
- **尺寸优化**：根据模型要求调整图片尺寸，减少传输量
- **并发处理**：多模态数据处理与网络请求并行
- **内存管理**：及时释放大型多媒体数据，避免内存泄漏

## 5. 用户体验设计

### 5.1 命令行接口
```bash
stress_tool [OPTIONS] --url <URL>

OPTIONS:
    -m, --model <MODEL>              模型名称 [default: gpt-3.5-turbo]
    -u, --url <URL>                  API 端点 URL
    -c, --concurrency <NUM>          并发数 [default: 1]
    -n, --number <NUM>               总请求数 [default: 100]
    -d, --dataset <FILE>             数据集文件路径
    -t, --max-tokens <NUM>           最大生成 tokens [default: 512]
        --temperature <FLOAT>        采样温度 [default: 0.1]
        --stream                     启用流式模式
        --timeout <SECONDS>          请求超时时间 [default: 30]
    -o, --output <DIR>               输出目录 [default: benchmark]

    # 多模态相关选项
        --multimodal                 启用多模态模式
        --image-base-path <PATH>     图片文件基础路径
        --video-base-path <PATH>     视频文件基础路径
        --max-image-size <PIXELS>    最大图片尺寸 [default: 1024]
        --image-quality <QUALITY>    图片质量 1-100 [default: 85]

    -h, --help                       显示帮助信息
```

### 5.2 输出格式
- **实时进度**：显示当前进度和实时 QPS，包括多模态数据处理进度
- **结果文件**：JSON 格式的详细报告，包含多模态相关指标
- **日志文件**：包含所有请求的详细日志，记录多模态数据处理过程

### 5.3 多模态使用示例
```bash
# 图片压测
stress_tool --multimodal \
  --model qwen2.5-vl-32b-instruct \
  --url https://api.example.com/v1/chat/completions \
  --dataset multimodal_images.jsonl \
  --image-base-path ./test_images \
  --concurrency 4 \
  --number 100

# 视频压测
stress_tool --multimodal \
  --model qwen2.5-vl-32b-instruct \
  --url https://api.example.com/v1/chat/completions \
  --dataset multimodal_videos.jsonl \
  --video-base-path ./test_videos \
  --concurrency 2 \
  --number 50
```

## 6. 实现计划

### 6.1 MVP 阶段（v0.1.0）
- [ ] 基础 CLI 参数解析
- [ ] HTTP 客户端实现
- [ ] 简单的并发压测
- [ ] 基础指标统计

### 6.2 完善阶段（v0.2.0）
- [ ] 流式响应支持
- [ ] Tokenizer 集成
- [ ] 详细的延迟统计
- [ ] 错误处理和重试

### 6.3 多模态阶段（v0.3.0）
- [ ] 多模态数据集格式支持
- [ ] 图片处理和编码
- [ ] 视频处理和帧提取
- [ ] 多模态请求构建
- [ ] 多模态指标统计

### 6.4 优化阶段（v0.4.0）
- [ ] 性能优化
- [ ] 更多数据集格式支持
- [ ] 可视化报告
- [ ] 配置文件支持

## 7. 质量保证

### 7.1 测试策略
- 单元测试：各模块功能测试，包括多模态处理模块
- 集成测试：端到端压测流程，覆盖多模态场景
- 性能测试：工具本身的性能验证，多模态数据处理性能
- 兼容性测试：不同格式的图片、视频文件处理

### 7.2 错误处理
- 网络错误重试机制
- 优雅的超时处理
- 详细的错误日志记录
- 多模态数据处理错误处理（文件不存在、格式不支持等）

## 8. 部署和分发

### 8.1 构建方式
- 支持多平台编译（Linux、macOS、Windows）
- 提供预编译二进制文件
- 支持 Docker 容器化部署
- 多模态依赖库的静态链接或动态链接配置

### 8.2 配置管理
- 命令行参数优先级最高
- 支持配置文件（YAML/JSON）
- 环境变量支持
- 多模态相关配置的验证和默认值设置