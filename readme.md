# stress_tool 输出指标说明

```json
{
  /* ========== 测试整体信息 ========== */
  "test_id": "20250717143000",               // 测试批次号，由 stress_tool 自动生成，格式：YYYYMMDDHHMMSS
  "test_config": {                           // 本次压测的所有输入参数
    "model": "my-llama-3-8B",                // 被测模型名
    "api_base": "http://127.0.0.1:8000/v1",  // 推理服务地址
    "concurrency": 8,                        // 并发数（多少个协程/线程同时发请求）
    "total_requests": 1000,                  // 计划发出的总请求数
    "dataset": "openqa-500.jsonl",           // 使用的评测数据集
    "max_tokens": 512,                       // 每个请求生成的最大 token 数
    "temperature": 0.1,                      // 采样温度
    "stream": true                           // 是否开启流式返回
  },

  /* ========== 请求统计 ========== */
  "total_requests": 1000,          // 实际发出的请求数
  "succeed_requests": 998,         // HTTP 200 且返回 JSON 正常的请求
  "failed_requests": 2,            // 网络错误/超时/非 200 状态码/解析失败的请求
  "total_prompt_tokens": 245360,   // 所有请求的 prompt token 总数（由本地 tokenizer 预估）
  "total_completion_tokens": 478912,// 所有请求返回的 completion token 总数（由本地 tokenizer 预估）

  /* ========== 时间相关指标 ========== */
  "test_duration_seconds": 63.47,  // 从第 1 个请求发出到最后一个请求响应完成的总耗时
  "first_request_sent": "2025-07-17T14:30:01.123Z",
  "last_request_done":    "2025-07-17T14:31:04.593Z",

  /* ========== 吞吐量指标 ========== */
  "throughput_tokens_per_sec": 7546.8,   // = total_completion_tokens / test_duration_seconds
  "qps": 15.75,                          // = succeed_requests / test_duration_seconds

  /* ========== 延迟指标（毫秒） ========== */
  "average_latency_ms": 496.3,           // 所有成功请求“端到端”延迟的算术平均
                                          // 计算方式：对每个请求记录 (response_done - request_sent)
  "average_time_to_first_token_ms": 38.7,// 对流式返回，指从发送请求到收到首包 chunk 的用时
                                          // 非流式场景下该值与 average_latency_ms 相同
  "average_time_per_output_token_ms": 9.1, // = 平均生成阶段耗时 / 平均输出 token 数
                                           // 生成阶段耗时 = (last_chunk_time - first_chunk_time)
  "p50_latency_ms": 471,                 // 50% 请求延迟 ≤ 471 ms
  "p90_latency_ms": 612,                 // 90% 请求延迟 ≤ 612 ms
  "p95_latency_ms": 689,
  "p99_latency_ms": 812,

  /* ========== 输入/输出长度统计 ========== */
  "avg_prompt_tokens": 245.8,            // = total_prompt_tokens / succeed_requests
  "avg_completion_tokens": 479.9,        // = total_completion_tokens / succeed_requests

  /* ========== 失败统计 ========== */
  "failures": [                          // 仅列出前 10 条，便于排查；全量请查日志
    {
      "request_id": "req-000017",
      "error": "timeout",
      "elapsed_ms": 30002
    },
    {
      "request_id": "req-000234",
      "error": "HTTP 503",
      "elapsed_ms": 45
    }
  ],

  /* ========== 系统资源快照（可选） ========== */
  "system_info": {
    "cpu_count": 16,
    "memory_total_gb": 62.8,
    "gpu_count": 1,
    "gpu_memory_total_mb": 24576,
    "stress_tool_version": "0.4.2"
  },

  /* ========== 原始直方图（可选，用于重绘曲线） ========== */
  "latency_histogram": [                 // 毫秒级桶，每 50 ms 一个桶
    [0, 50, 0],
    [50, 100, 2],
    [100, 150, 5],
    ...
    [800, 850, 1]
  ]
}
```

---

### 指标是如何测得的（测试方法简述）

| 指标 | 采集位置 | 说明 |
|---|---|---|
| total_requests | 压测端计数器 | 每发一条 HTTP 请求就 +1 |
| succeed_requests | 收到 HTTP 200 且 JSON 解析成功 | 否则计入 failed_requests |
| total_prompt_tokens | 本地 tokenizer | 在发送前把 prompt 编码一次即可得到长度 |
| total_completion_tokens | 本地 tokenizer | 把返回的 `choices[0].text` 或 `delta.content` 拼起来再编码 |
| average_latency_ms | 压测端计时 | `time_end - time_start`，`time_start` 为请求发出瞬间，`time_end` 为收到最后一个字节（非流式）或收到最后一个 `finish_reason=stop` 的 chunk（流式） |
| average_time_to_first_token_ms | 流式场景 | 收到第一个非空 chunk 的时间 - `time_start` |
| average_time_per_output_token_ms | 流式场景 | `(last_chunk_time - first_chunk_time) / (completion_tokens - 1)` |
| throughput_tokens_per_sec | 计算得到 | 所有成功请求的 completion_tokens 之和 ÷ 总耗时 |
| qps | 计算得到 | succeed_requests ÷ 总耗时 |
| pXX_latency_ms | 排序后取分位 | 把所有成功请求的延迟放进数组，排序后取对应百分位 |

---

### 如何复现查看

1. 运行  
   ```bash
   stress_tool \
     --model my-llama-3-8B \
     --url http://127.0.0.1:8000/v1/chat/completions \
     --concurrency 8 \
     --number 1000
   ```
2. 结束后在 `benchmark/20250717143000/` 目录下即可找到 `result.json` 和 `logs.txt`。
