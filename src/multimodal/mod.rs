pub mod image;
pub mod request;
pub mod utils;

#[cfg(feature = "video")]
pub mod video;

use serde::{Deserialize, Serialize};
use std::path::PathBuf;

/// Content type for multimodal messages
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(tag = "type")]
pub enum ContentType {
    #[serde(rename = "text")]
    Text { text: String },
    
    #[serde(rename = "image")]
    Image {
        path: String,
        #[serde(skip_serializing_if = "Option::is_none")]
        description: Option<String>,
        #[serde(skip_serializing_if = "Option::is_none")]
        resize: Option<ImageResize>,
    },
    
    #[serde(rename = "image_url")]
    ImageUrl {
        image_url: ImageUrl,
    },
    
    #[cfg(feature = "video")]
    #[serde(rename = "video")]
    Video {
        path: String,
        #[serde(skip_serializing_if = "Option::is_none")]
        description: Option<String>,
        #[serde(skip_serializing_if = "Option::is_none")]
        options: Option<VideoOptions>,
    },
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct ImageUrl {
    pub url: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ImageResize {
    #[serde(skip_serializing_if = "Option::is_none")]
    pub max_width: Option<u32>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub max_height: Option<u32>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub quality: Option<u8>,
}

#[cfg(feature = "video")]
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VideoOptions {
    #[serde(skip_serializing_if = "Option::is_none")]
    pub fps: Option<f32>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub max_frames: Option<u32>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub start_time: Option<f32>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub duration: Option<f32>,
}

/// Message in a conversation
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Message {
    pub role: String,
    pub content: MessageContent,
}

/// Content of a message, can be string or array of content types
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(untagged)]
pub enum MessageContent {
    Text(String),
    Array(Vec<ContentType>),
}

/// Dataset entry for multimodal testing
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DatasetEntry {
    #[serde(skip_serializing_if = "Option::is_none")]
    pub prompt: Option<String>,
    
    #[serde(skip_serializing_if = "Option::is_none")]
    pub messages: Option<Vec<Message>>,
    
    #[serde(skip_serializing_if = "Option::is_none")]
    pub expected_tokens: Option<u32>,
    
    #[serde(skip_serializing_if = "Option::is_none")]
    pub metadata: Option<serde_json::Value>,
    
    #[serde(skip_serializing_if = "Option::is_none")]
    pub _config: Option<DatasetConfig>,
}

/// Configuration that can be embedded in dataset
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DatasetConfig {
    #[serde(skip_serializing_if = "Option::is_none")]
    pub image_base_path: Option<String>,
    
    #[serde(skip_serializing_if = "Option::is_none")]
    pub video_base_path: Option<String>,
    
    #[serde(skip_serializing_if = "Option::is_none")]
    pub default_image_quality: Option<u8>,
    
    #[serde(skip_serializing_if = "Option::is_none")]
    pub default_video_fps: Option<f32>,
}

/// Processed multimodal content ready for API request
#[derive(Debug, Clone)]
pub struct ProcessedContent {
    pub content_type: String,
    pub data: ProcessedData,
}

#[derive(Debug, Clone)]
pub enum ProcessedData {
    Text(String),
    ImageBase64(String),
    #[cfg(feature = "video")]
    VideoFrames(Vec<String>), // Base64 encoded frames
}

/// Statistics for multimodal processing
#[derive(Debug, Clone, Default)]
pub struct MultimodalStats {
    pub images_processed: u64,
    pub videos_processed: u64,
    pub total_image_processing_time_ms: u64,
    pub total_video_processing_time_ms: u64,
    pub total_image_size_bytes: u64,
    pub total_video_size_bytes: u64,
    pub image_processing_errors: u64,
    pub video_processing_errors: u64,
}

impl MultimodalStats {
    pub fn add_image_processing(&mut self, processing_time_ms: u64, size_bytes: u64) {
        self.images_processed += 1;
        self.total_image_processing_time_ms += processing_time_ms;
        self.total_image_size_bytes += size_bytes;
    }
    
    pub fn add_video_processing(&mut self, processing_time_ms: u64, size_bytes: u64) {
        self.videos_processed += 1;
        self.total_video_processing_time_ms += processing_time_ms;
        self.total_video_size_bytes += size_bytes;
    }
    
    pub fn add_image_error(&mut self) {
        self.image_processing_errors += 1;
    }
    
    pub fn add_video_error(&mut self) {
        self.video_processing_errors += 1;
    }
    
    pub fn average_image_processing_time_ms(&self) -> f64 {
        if self.images_processed > 0 {
            self.total_image_processing_time_ms as f64 / self.images_processed as f64
        } else {
            0.0
        }
    }
    
    pub fn average_video_processing_time_ms(&self) -> f64 {
        if self.videos_processed > 0 {
            self.total_video_processing_time_ms as f64 / self.videos_processed as f64
        } else {
            0.0
        }
    }
}

/// Error types for multimodal processing
#[derive(thiserror::Error, Debug)]
pub enum MultimodalError {
    #[error("Image processing error: {0}")]
    ImageError(#[from] image::ImageError),
    
    #[error("IO error: {0}")]
    IoError(#[from] std::io::Error),
    
    #[error("Base64 encoding error: {0}")]
    Base64Error(#[from] base64::DecodeError),
    
    #[error("Unsupported file format: {0}")]
    UnsupportedFormat(String),
    
    #[error("File not found: {0}")]
    FileNotFound(PathBuf),
    
    #[cfg(feature = "video")]
    #[error("Video processing error: {0}")]
    VideoError(String),
    
    #[error("Invalid configuration: {0}")]
    ConfigError(String),
}

pub type Result<T> = std::result::Result<T, MultimodalError>;
