use anyhow::Context;
use clap::Parser;
use serde::{Deserialize, Serialize};
use std::path::PathBuf;

#[derive(Pa<PERSON><PERSON>, Debug, Clone)]
#[command(name = "stress_tool")]
#[command(about = "A stress testing tool for LLM and multimodal model inference services")]
pub struct Args {
    /// Model name
    #[arg(short, long, default_value = "gpt-3.5-turbo")]
    pub model: String,

    /// API endpoint URL
    #[arg(short, long)]
    pub url: String,

    /// Number of concurrent requests
    #[arg(short, long, default_value = "1")]
    pub concurrency: u32,

    /// Total number of requests to send
    #[arg(short, long, default_value = "100")]
    pub number: u32,

    /// Dataset file path
    #[arg(short, long)]
    pub dataset: Option<PathBuf>,

    /// Maximum tokens to generate
    #[arg(short = 't', long, default_value = "512")]
    pub max_tokens: u32,

    /// Sampling temperature
    #[arg(long, default_value = "0.1")]
    pub temperature: f32,

    /// Enable streaming mode
    #[arg(long)]
    pub stream: bool,

    /// Request timeout in seconds
    #[arg(long, default_value = "30")]
    pub timeout: u64,

    /// Output directory
    #[arg(short, long, default_value = "benchmark")]
    pub output: PathBuf,

    /// Enable multimodal mode
    #[arg(long)]
    pub multimodal: bool,

    /// Base path for image files
    #[arg(long)]
    pub image_base_path: Option<PathBuf>,

    /// Base path for video files
    #[arg(long)]
    pub video_base_path: Option<PathBuf>,

    /// Maximum image size in pixels
    #[arg(long, default_value = "1024")]
    pub max_image_size: u32,

    /// Image quality (1-100)
    #[arg(long, default_value = "85")]
    pub image_quality: u8,

    /// API key for authentication
    #[arg(long)]
    pub api_key: Option<String>,

    /// Verbose logging
    #[arg(short, long)]
    pub verbose: bool,

    /// Configuration file path (YAML or TOML)
    #[arg(short = 'f', long)]
    pub config_file: Option<PathBuf>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Config {
    pub model: String,
    pub api_base: String,
    pub concurrency: u32,
    pub total_requests: u32,
    pub dataset: Option<PathBuf>,
    pub max_tokens: u32,
    pub temperature: f32,
    pub stream: bool,
    pub timeout: u64,
    pub output_dir: PathBuf,
    
    // Multimodal configuration
    pub multimodal: bool,
    pub image_base_path: Option<PathBuf>,
    pub video_base_path: Option<PathBuf>,
    pub max_image_size: u32,
    pub image_quality: u8,
    
    // Authentication
    pub api_key: Option<String>,
    
    // Logging
    pub verbose: bool,
}

impl Config {
    /// Create config from command line args, optionally loading from config file
    pub fn from_args(args: Args) -> anyhow::Result<Self> {
        let mut config = if let Some(config_file) = &args.config_file {
            Self::load_from_file(config_file)?
        } else {
            Self::default()
        };

        // Override with command line arguments (CLI takes precedence)
        config.apply_args_overrides(args);

        Ok(config)
    }

    /// Load configuration from file (YAML or TOML)
    fn load_from_file(path: &PathBuf) -> anyhow::Result<Self> {
        let content = std::fs::read_to_string(path)
            .with_context(|| format!("Failed to read config file: {}", path.display()))?;

        let config = match path.extension().and_then(|ext| ext.to_str()) {
            Some("yaml") | Some("yml") => {
                serde_yaml::from_str(&content)
                    .with_context(|| format!("Failed to parse YAML config file: {}", path.display()))?
            }
            Some("toml") => {
                toml::from_str(&content)
                    .with_context(|| format!("Failed to parse TOML config file: {}", path.display()))?
            }
            _ => {
                anyhow::bail!("Unsupported config file format. Use .yaml, .yml, or .toml");
            }
        };

        Ok(config)
    }

    /// Apply command line argument overrides to config
    fn apply_args_overrides(&mut self, args: Args) {
        // Only override if the argument was explicitly provided
        if args.model != "gpt-3.5-turbo" { // Check if not default
            self.model = args.model;
        }
        if !args.url.is_empty() {
            self.api_base = args.url;
        }
        if args.concurrency != 1 {
            self.concurrency = args.concurrency;
        }
        if args.number != 100 {
            self.total_requests = args.number;
        }
        if args.dataset.is_some() {
            self.dataset = args.dataset;
        }
        if args.max_tokens != 512 {
            self.max_tokens = args.max_tokens;
        }
        if args.temperature != 0.1 {
            self.temperature = args.temperature;
        }
        if args.stream {
            self.stream = args.stream;
        }
        if args.timeout != 30 {
            self.timeout = args.timeout;
        }
        if args.output != PathBuf::from("benchmark") {
            self.output_dir = args.output;
        }
        if args.multimodal {
            self.multimodal = args.multimodal;
        }
        if args.image_base_path.is_some() {
            self.image_base_path = args.image_base_path;
        }
        if args.video_base_path.is_some() {
            self.video_base_path = args.video_base_path;
        }
        if args.max_image_size != 1024 {
            self.max_image_size = args.max_image_size;
        }
        if args.image_quality != 85 {
            self.image_quality = args.image_quality;
        }
        if args.api_key.is_some() {
            self.api_key = args.api_key;
        }
        if args.verbose {
            self.verbose = args.verbose;
        }
    }
}

impl Default for Config {
    fn default() -> Self {
        Self {
            model: "gpt-3.5-turbo".to_string(),
            api_base: "http://localhost:8000/v1/chat/completions".to_string(),
            concurrency: 1,
            total_requests: 100,
            dataset: None,
            max_tokens: 512,
            temperature: 0.1,
            stream: false,
            timeout: 30,
            output_dir: PathBuf::from("benchmark"),
            multimodal: false,
            image_base_path: None,
            video_base_path: None,
            max_image_size: 1024,
            image_quality: 85,
            api_key: None,
            verbose: false,
        }
    }
}

impl Config {
    pub fn validate(&self) -> anyhow::Result<()> {
        if self.concurrency == 0 {
            anyhow::bail!("Concurrency must be greater than 0");
        }
        
        if self.total_requests == 0 {
            anyhow::bail!("Total requests must be greater than 0");
        }
        
        if self.max_tokens == 0 {
            anyhow::bail!("Max tokens must be greater than 0");
        }
        
        if !(0.0..=2.0).contains(&self.temperature) {
            anyhow::bail!("Temperature must be between 0.0 and 2.0");
        }
        
        if self.timeout == 0 {
            anyhow::bail!("Timeout must be greater than 0");
        }
        
        if self.multimodal {
            if self.image_quality == 0 || self.image_quality > 100 {
                anyhow::bail!("Image quality must be between 1 and 100");
            }
            
            if self.max_image_size == 0 {
                anyhow::bail!("Max image size must be greater than 0");
            }
        }
        
        Ok(())
    }
    
    pub fn resolve_image_path(&self, path: &str) -> PathBuf {
        let path = PathBuf::from(path);
        if path.is_absolute() {
            path
        } else if let Some(base) = &self.image_base_path {
            base.join(path)
        } else if let Some(dataset) = &self.dataset {
            dataset.parent().unwrap_or_else(|| std::path::Path::new(".")).join(path)
        } else {
            path
        }
    }
    
    pub fn resolve_video_path(&self, path: &str) -> PathBuf {
        let path = PathBuf::from(path);
        if path.is_absolute() {
            path
        } else if let Some(base) = &self.video_base_path {
            base.join(path)
        } else if let Some(dataset) = &self.dataset {
            dataset.parent().unwrap_or_else(|| std::path::Path::new(".")).join(path)
        } else {
            path
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_config_validation() {
        let mut config = Config {
            model: "test".to_string(),
            api_base: "http://localhost:8000".to_string(),
            concurrency: 1,
            total_requests: 100,
            dataset: None,
            max_tokens: 512,
            temperature: 0.1,
            stream: false,
            timeout: 30,
            output_dir: PathBuf::from("benchmark"),
            multimodal: false,
            image_base_path: None,
            video_base_path: None,
            max_image_size: 1024,
            image_quality: 85,
            api_key: None,
            verbose: false,
        };
        
        assert!(config.validate().is_ok());
        
        config.concurrency = 0;
        assert!(config.validate().is_err());
        
        config.concurrency = 1;
        config.temperature = 3.0;
        assert!(config.validate().is_err());
    }
    
    #[test]
    fn test_path_resolution() {
        let config = Config {
            model: "test".to_string(),
            api_base: "http://localhost:8000".to_string(),
            concurrency: 1,
            total_requests: 100,
            dataset: Some(PathBuf::from("/data/test.jsonl")),
            max_tokens: 512,
            temperature: 0.1,
            stream: false,
            timeout: 30,
            output_dir: PathBuf::from("benchmark"),
            multimodal: true,
            image_base_path: Some(PathBuf::from("/images")),
            video_base_path: None,
            max_image_size: 1024,
            image_quality: 85,
            api_key: None,
            verbose: false,
        };
        
        // Test absolute path
        assert_eq!(config.resolve_image_path("/absolute/path.jpg"), PathBuf::from("/absolute/path.jpg"));
        
        // Test relative path with base path
        assert_eq!(config.resolve_image_path("relative/path.jpg"), PathBuf::from("/images/relative/path.jpg"));
    }
}
